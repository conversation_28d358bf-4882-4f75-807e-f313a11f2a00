import React, { createContext, useContext, useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import api from '../utils/api';

// Define types
export type UserRole = 'admin' | 'nurse' | 'patient';

export type User = {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  status: string;
};

type AuthContextType = {
  user: User | null;
  token: string | null;
  isLoading: boolean;
  error: string | null;
  login: (email: string, password: string) => Promise<void>;
  register: (userData: RegisterFormData) => Promise<void>;
  logout: () => void;
  clearError: () => void;
};

export type RegisterFormData = {
  name: string;
  email: string;
  password: string;
  phone: string;
  role: UserRole;
  coordinates: [number, number];
  address?: string;
  licenseNumber?: string;
  yearsOfExperience?: number;
  specializations?: string[];
  education?: string;
  certifications?: string[];
  hourlyRate?: number;
  bio?: string;
};

// Create the context with a default value
const AuthContext = createContext<AuthContextType>({
  user: null,
  token: null,
  isLoading: false,
  error: null,
  login: async () => {},
  register: async () => {},
  logout: () => {},
  clearError: () => {}
});

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  // Initialize auth state from localStorage
  useEffect(() => {
    const storedUser = localStorage.getItem('user');
    const storedToken = localStorage.getItem('token');

    if (storedUser && storedToken) {
      setUser(JSON.parse(storedUser));
      setToken(storedToken);
      // The API utility will automatically add the token to requests
    }
    
    setIsLoading(false);
  }, []);

  const login = async (email: string, password: string) => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await api.post('/auth/login', {
        email,
        password
      });

      // Check if response has the expected structure
      if (response.data?.access_token && response.data?.user) {
        const { access_token, user } = response.data;
        
        // Store in state
        setToken(access_token);
        setUser(user);
        
        // Store in localStorage
        localStorage.setItem('token', access_token);
        localStorage.setItem('user', JSON.stringify(user));
        
        // Redirect based on role
        if (user.role === 'admin') {
          router.push('/admin/dashboard');
        } else if (user.role === 'nurse') {
          router.push('/nurse/dashboard');
        } else {
          router.push('/dashboard');
        }
      } else {
        throw new Error('Invalid response format from server');
      }
    } catch (err: any) {
      console.error('Login error:', err);
      
      let errorMessage;
      if (err.message === 'Network Error') {
        errorMessage = 'Network error. Please check your internet connection or ensure the backend server is running.';
      } else {
        errorMessage = 
          err.response?.data?.message || 
          err.message || 
          'An error occurred during login';
      }
      
      setError(Array.isArray(errorMessage) ? errorMessage.join(', ') : errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (userData: RegisterFormData) => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await api.post('/auth/register', userData);

      if (response.data?.access_token && response.data?.user) {
        const { access_token, user } = response.data;
        
        // Store in state
        setToken(access_token);
        setUser(user);
        
        // Store in localStorage
        localStorage.setItem('token', access_token);
        localStorage.setItem('user', JSON.stringify(user));
        
        // If user is a nurse, redirect to a "verification pending" page
        if (user.role === 'nurse' && user.status === 'pending') {
          router.push('/verification-pending');
        } else {
          // Otherwise redirect based on role
          if (user.role === 'admin') {
            router.push('/admin/dashboard');
          } else if (user.role === 'nurse') {
            router.push('/nurse/dashboard');
          } else {
            router.push('/dashboard');
          }
        }
      } else {
        throw new Error('Invalid response format from server');
      }
    } catch (err: any) {
      console.error('Registration error:', err);
      
      let errorMessage;
      if (err.message === 'Network Error') {
        errorMessage = 'Network error. Please check your internet connection or ensure the backend server is running.';
      } else {
        errorMessage = 
          err.response?.data?.message || 
          err.message || 
          'An error occurred during registration';
      }
      
      setError(Array.isArray(errorMessage) ? errorMessage.join(', ') : errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    // Clear state
    setUser(null);
    setToken(null);
    
    // Clear localStorage
    localStorage.removeItem('user');
    localStorage.removeItem('token');
    
    // Redirect to home page
    router.push('/');
  };

  const clearError = () => {
    setError(null);
  };

  return (
    <AuthContext.Provider value={{
      user,
      token,
      isLoading,
      error,
      login,
      register,
      logout,
      clearError
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    console.error('AuthContext not available - make sure you are using AuthProvider');
    // Rather than throwing an error, return a default context
    return {
      user: null,
      token: null,
      isLoading: false,
      error: 'AuthContext not available',
      login: async () => { console.error('Auth not initialized'); },
      register: async () => { console.error('Auth not initialized'); },
      logout: () => { console.error('Auth not initialized'); },
      clearError: () => { console.error('Auth not initialized'); }
    };
  }
  return context;
};
