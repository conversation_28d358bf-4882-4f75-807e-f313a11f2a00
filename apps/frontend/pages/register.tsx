import Link from 'next/link';
import { useState } from 'react';
import { useAuth } from '../context/AuthContext';

export default function Register() {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [phone, setPhone] = useState('');
  const [role, setRole] = useState<'nurse' | 'patient'>('patient');
  const [licenseNumber, setLicenseNumber] = useState('');
  const [yearsOfExperience, setYearsOfExperience] = useState('');
  const [formErrors, setFormErrors] = useState<{[key: string]: string}>({});
  const { register, isLoading, error, clearError } = useAuth();
  
  // Validate phone number (Egyptian format)
  const validateEgyptianPhone = (phone: string) => {
    // Only accept Egyptian local mobile numbers (01X followed by 8 digits)
    // Valid formats: 010, 011, 012, 015 followed by 8 digits
    const egyptianPhoneRegex = /^01[0125]\d{8}$/;
    
    // Debug log for phone validation
    console.log('Validating phone:', phone, egyptianPhoneRegex.test(phone));
    
    return egyptianPhoneRegex.test(phone);
  };
  
  // Validate password complexity
  const validatePasswordComplexity = (password: string) => {
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/;
    return passwordRegex.test(password);
  };

  const validateForm = () => {
    const errors: {[key: string]: string} = {};
    
    if (!name || name.length < 2) {
      errors.name = 'Name must be at least 2 characters long';
    }
    
    if (!email || !/\S+@\S+\.\S+/.test(email)) {
      errors.email = 'Please provide a valid email address';
    }
    
    if (!password) {
      errors.password = 'Password is required';
    } else if (!validatePasswordComplexity(password)) {
      errors.password = 'Password must be at least 8 characters with lowercase, uppercase, and numbers';
    }
    
    if (password !== confirmPassword) {
      errors.confirmPassword = 'Passwords do not match';
    }
    
    if (!phone) {
      errors.phone = 'Phone number is required';
    } else if (!validateEgyptianPhone(phone)) {
      errors.phone = 'Please provide a valid Egyptian mobile number (01X format, 11 digits)';
    }
    
    if (role === 'nurse') {
      if (!licenseNumber || licenseNumber.length < 5) {
        errors.licenseNumber = 'License number is required for nurses (min 5 characters)';
      }
      
      if (!yearsOfExperience) {
        errors.yearsOfExperience = 'Years of experience is required for nurses';
      } else {
        const years = parseInt(yearsOfExperience);
        if (isNaN(years) || years < 0 || years > 50) {
          errors.yearsOfExperience = 'Years of experience must be between 0 and 50';
        }
      }
    }
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    clearError();
    
    if (!validateForm()) {
      return;
    }
    
    // Default coordinates for Egypt (can be improved with geolocation)
    const coordinates: [number, number] = [31.2357, 30.0444]; // [longitude, latitude]

    // Create the user data object based on form inputs
    const userData = {
      name,
      email,
      password,
      phone,
      role,
      coordinates,
      // Add nurse-specific fields if the role is nurse
      ...(role === 'nurse' && {
        licenseNumber,
        yearsOfExperience: parseInt(yearsOfExperience, 10),
        specializations: ['general'], // Default specialization
      })
    };

    try {
      await register(userData);
    } catch (e) {
      // Error handling is done in the AuthContext
      console.error("Registration error:", e);
    }
  };

  return (
    <div className="flex h-screen bg-gradient-to-r from-blue-400 via-white-500 to-white-500">
      <div className="w-full flex items-center justify-center">
        <div className="container mx-auto p-6 bg-white/80 backdrop-blur-md rounded-xl shadow-2xl flex flex-col md:flex-row items-center">
          <div className="w-full md:w-1/2 p-4">
            <img src="/imagenurse3.jpeg" alt="Register Background" className="w-full h-auto rounded-lg transform hover:scale-105 transition duration-300" />
          </div>
          <div className="w-full md:w-1/2 p-8 relative">
            <div className="flex justify-between items-center mb-6">
              <Link href="/" className="text-blue-600 hover:text-blue-800">
                <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                  <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </Link>
              <div className="text-3xl text-gray-800 font-cursive">logo</div>
            </div>
            <h2 className="text-4xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600 text-center mb-6">Register</h2>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label className="block text-lg font-semibold text-gray-700">Name</label>
                <input
                  type="text"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  className={`mt-2 w-full border-b-2 ${formErrors.name ? 'border-red-500' : 'border-purple-300'} focus:border-purple-600 focus:outline-none text-xl text-gray-800 placeholder-gray-400 transition duration-300`}
                  placeholder="Enter your name"
                />
                {formErrors.name && (
                  <p className="text-red-500 text-xs mt-1">{formErrors.name}</p>
                )}
              </div>
              <div>
                <label className="block text-lg font-semibold text-gray-700">Email</label>
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className={`mt-2 w-full border-b-2 ${formErrors.email ? 'border-red-500' : 'border-purple-300'} focus:border-purple-600 focus:outline-none text-xl text-gray-800 placeholder-gray-400 transition duration-300`}
                  placeholder="Enter your email"
                />
                {formErrors.email && (
                  <p className="text-red-500 text-xs mt-1">{formErrors.email}</p>
                )}
              </div>
              <div>
                <label className="block text-lg font-semibold text-gray-700">Password</label>
                <input
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className={`mt-2 w-full border-b-2 ${formErrors.password ? 'border-red-500' : 'border-purple-300'} focus:border-purple-600 focus:outline-none text-xl text-gray-800 placeholder-gray-400 transition duration-300`}
                  placeholder="Enter your password"
                />
                {formErrors.password && (
                  <p className="text-red-500 text-xs mt-1">{formErrors.password}</p>
                )}
              </div>
              <div>
                <label className="block text-lg font-semibold text-gray-700">Confirm Password</label>
                <input
                  type="password"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  className={`mt-2 w-full border-b-2 ${formErrors.confirmPassword ? 'border-red-500' : 'border-purple-300'} focus:border-purple-600 focus:outline-none text-xl text-gray-800 placeholder-gray-400 transition duration-300`}
                  placeholder="Confirm your password"
                />
                {formErrors.confirmPassword && (
                  <p className="text-red-500 text-xs mt-1">{formErrors.confirmPassword}</p>
                )}
              </div>
              <div>
                <label className="block text-lg font-semibold text-gray-700">Phone</label>
                <input
                  type="tel"
                  value={phone}
                  onChange={(e) => setPhone(e.target.value)}
                  className={`mt-2 w-full border-b-2 ${formErrors.phone ? 'border-red-500' : 'border-purple-300'} focus:border-purple-600 focus:outline-none text-xl text-gray-800 placeholder-gray-400 transition duration-300`}
                  placeholder="Enter your phone (01X format)"
                />
                {formErrors.phone && (
                  <p className="text-red-500 text-xs mt-1">{formErrors.phone}</p>
                )}
              </div>
              <div>
                <label className="block text-lg font-semibold text-gray-700">I am</label>
                <div className="flex space-x-4 mt-2">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      value="nurse"
                      checked={role === 'nurse'}
                      onChange={(e) => setRole(e.target.value as 'nurse' | 'patient')}
                      className="mr-2 h-5 w-5 text-purple-600"
                    /> 
                    <span className="text-xl text-gray-800">Nurse</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      value="patient"
                      checked={role === 'patient'}
                      onChange={(e) => setRole(e.target.value as 'nurse' | 'patient')}
                      className="mr-2 h-5 w-5 text-purple-600"
                    /> 
                    <span className="text-xl text-gray-800">Patient</span>
                  </label>
                </div>
              </div>
              {role === 'nurse' && (
                <>
                  <div>
                    <label className="block text-lg font-semibold text-gray-700">License Number</label>
                    <input
                      type="text"
                      value={licenseNumber}
                      onChange={(e) => setLicenseNumber(e.target.value)}
                      className={`mt-2 w-full border-b-2 ${formErrors.licenseNumber ? 'border-red-500' : 'border-purple-300'} focus:border-purple-600 focus:outline-none text-xl text-gray-800 placeholder-gray-400 transition duration-300`}
                      placeholder="Enter license number"
                    />
                    {formErrors.licenseNumber && (
                      <p className="text-red-500 text-xs mt-1">{formErrors.licenseNumber}</p>
                    )}
                  </div>
                  <div>
                    <label className="block text-lg font-semibold text-gray-700">Years of Experience</label>
                    <input
                      type="number"
                      value={yearsOfExperience}
                      onChange={(e) => setYearsOfExperience(e.target.value)}
                      className={`mt-2 w-full border-b-2 ${formErrors.yearsOfExperience ? 'border-red-500' : 'border-purple-300'} focus:border-purple-600 focus:outline-none text-xl text-gray-800 placeholder-gray-400 transition duration-300`}
                      placeholder="Enter years of experience"
                    />
                    {formErrors.yearsOfExperience && (
                      <p className="text-red-500 text-xs mt-1">{formErrors.yearsOfExperience}</p>
                    )}
                  </div>
                </>
              )}
              {error && <p className="text-red-500 text-md font-medium">{error}</p>}
              <p className="text-md text-gray-600">Already have an account? <Link href="/login" className="text-purple-600 hover:text-purple-800 font-medium">Login</Link></p>
              <button
                type="submit"
                disabled={isLoading}
                className={`w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 px-6 rounded-full text-lg font-semibold shadow-lg transform transition duration-300 ${
                  isLoading ? 'opacity-70 cursor-not-allowed' : 'hover:from-blue-700 hover:to-purple-700 hover:scale-105'
                }`}
              >
                {isLoading ? 'Registering...' : 'Register'}
              </button>
              <div className="flex justify-center space-x-4 mt-4">
                <button type="button" className="text-blue-600 text-2xl hover:text-blue-800 transition">f</button>
                <button type="button" className="text-black text-2xl hover:text-gray-800 transition">G</button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}
